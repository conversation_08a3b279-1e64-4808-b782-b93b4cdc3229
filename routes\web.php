<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApnController;
use App\Http\Controllers\VoipController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect('/apn');
});

// APN Demo Routes
Route::get('/apn', [ApnController::class, 'index'])->name('apn.index');
Route::post('/apn/send-test', [ApnController::class, 'sendTestNotification'])->name('apn.send-test');
Route::post('/apn/update-token', [ApnController::class, 'updateToken'])->name('apn.update-token');
Route::post('/apn/send-anonymous', [ApnController::class, 'sendAnonymousNotification'])->name('apn.send-anonymous');

// VoIP Call Routes
Route::get('/voip', [VoipController::class, 'index'])->name('voip.index');
Route::post('/voip/initiate-call', [VoipController::class, 'initiateCall'])->name('voip.initiate-call');
Route::post('/voip/update-call-status', [VoipController::class, 'updateCallStatus'])->name('voip.update-call-status');
Route::get('/voip/user-call-logs', [VoipController::class, 'getUserCallLogs'])->name('voip.user-call-logs');
Route::get('/voip/call-stats', [VoipController::class, 'getCallStats'])->name('voip.call-stats');
