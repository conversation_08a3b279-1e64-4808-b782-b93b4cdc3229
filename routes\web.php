<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApnController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect('/apn');
});

// APN Demo Routes
Route::get('/apn', [ApnController::class, 'index'])->name('apn.index');
Route::post('/apn/send-test', [ApnController::class, 'sendTestNotification'])->name('apn.send-test');
Route::post('/apn/update-token', [ApnController::class, 'updateToken'])->name('apn.update-token');
Route::post('/apn/send-anonymous', [ApnController::class, 'sendAnonymousNotification'])->name('apn.send-anonymous');
