<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CallLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'call_id',
        'caller_user_id',
        'receiver_user_id',
        'caller_name',
        'caller_number',
        'receiver_name',
        'receiver_number',
        'call_type',
        'call_status',
        'started_at',
        'answered_at',
        'ended_at',
        'duration_seconds',
        'metadata',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'answered_at' => 'datetime',
        'ended_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the caller user.
     */
    public function caller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'caller_user_id');
    }

    /**
     * Get the receiver user.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_user_id');
    }

    /**
     * Calculate call duration in human readable format.
     */
    public function getDurationFormattedAttribute(): string
    {
        if (!$this->duration_seconds) {
            return 'N/A';
        }

        $minutes = floor($this->duration_seconds / 60);
        $seconds = $this->duration_seconds % 60;

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Scope for incoming calls.
     */
    public function scopeIncoming($query)
    {
        return $query->where('call_type', 'incoming');
    }

    /**
     * Scope for outgoing calls.
     */
    public function scopeOutgoing($query)
    {
        return $query->where('call_type', 'outgoing');
    }

    /**
     * Scope for missed calls.
     */
    public function scopeMissed($query)
    {
        return $query->where('call_status', 'missed');
    }
}
