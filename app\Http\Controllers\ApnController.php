<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Notifications\ApnTestNotification;
use Illuminate\Support\Facades\Notification;

class ApnController extends Controller
{
    /**
     * Show the APN demo page
     */
    public function index()
    {
        $users = User::all();
        return view('apn.index', compact('users'));
    }

    /**
     * Send a test notification to a user
     */
    public function sendTestNotification(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:500',
        ]);

        $user = User::find($request->user_id);

        if (!$user->apn_token) {
            return back()->with('error', 'User does not have an APN token set.');
        }

        try {
            $user->notify(new ApnTestNotification($request->title, $request->body));
            return back()->with('success', 'APN notification sent successfully!');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to send notification: ' . $e->getMessage());
        }
    }

    /**
     * Update user's APN token
     */
    public function updateToken(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'apn_token' => 'required|string',
        ]);

        $user = User::find($request->user_id);
        $user->apn_token = $request->apn_token;
        $user->save();

        return back()->with('success', 'APN token updated successfully!');
    }

    /**
     * Send notification to anonymous notifiable (without user model)
     */
    public function sendAnonymousNotification(Request $request)
    {
        $request->validate([
            'apn_token' => 'required|string',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:500',
        ]);

        try {
            Notification::route('apn', $request->apn_token)
                ->notify(new ApnTestNotification($request->title, $request->body));

            return back()->with('success', 'Anonymous APN notification sent successfully!');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to send notification: ' . $e->getMessage());
        }
    }
}
