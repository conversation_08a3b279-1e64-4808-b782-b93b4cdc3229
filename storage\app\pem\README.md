# APN Private Key Directory

Place your Apple Push Notification service private key (.p8 file) in this directory.

## Expected File Structure

```
storage/app/pem/
├── AuthKey_W4LYFB48MX.p8  (Your actual .p8 file from Apple)
└── README.md              (This file)
```

## How to Get Your .p8 File

1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Click on "Keys" in the sidebar
4. Click the "+" button to create a new key
5. Enter a key name and check "Apple Push Notifications service (APNs)"
6. Click "Continue" and then "Register"
7. Download the .p8 file (you can only download it once!)
8. Rename it to match your Key ID (e.g., AuthKey_W4LYFB48MX.p8)
9. Place it in this directory

## File Format

Your .p8 file should look like this:

```
-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
[Base64 encoded private key content]
...
-----E<PERSON> PRIVATE KEY-----
```

## Configuration

Update your `.env` file:

```env
APN_KEY_ID=W4LYFB48MX
APN_TEAM_ID=T647P9HAZS
APN_BUNDLE_ID=com.yourapp.bundleid
APN_PRIVATE_KEY_PATH=  # Leave empty to use default path
APN_PRODUCTION=false
```

## Security Note

- Never commit your .p8 files to version control
- Keep your private keys secure
- The .p8 file contains sensitive cryptographic material
