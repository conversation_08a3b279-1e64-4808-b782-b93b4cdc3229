<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\CallLog;
use App\Notifications\IncomingCallNotification;
use Illuminate\Support\Facades\Notification;

class VoipController extends Controller
{
    /**
     * Show call logs dashboard
     */
    public function index()
    {
        $callLogs = CallLog::with(['caller', 'receiver'])
            ->orderBy('started_at', 'desc')
            ->paginate(20);

        $users = User::all();

        return view('voip.index', compact('callLogs', 'users'));
    }

    /**
     * Initiate a VoIP call
     */
    public function initiateCall(Request $request)
    {
        $request->validate([
            'caller_user_id' => 'required|exists:users,id',
            'receiver_user_id' => 'required|exists:users,id|different:caller_user_id',
        ]);

        $caller = User::find($request->caller_user_id);
        $receiver = User::find($request->receiver_user_id);

        if (!$receiver->apn_token) {
            return back()->with('error', 'Receiver does not have a VoIP token set.');
        }

        $callId = uniqid('call_');

        // Create call log
        $callLog = CallLog::create([
            'call_id' => $callId,
            'caller_user_id' => $caller->id,
            'receiver_user_id' => $receiver->id,
            'caller_name' => $caller->name,
            'caller_number' => $caller->phone ?? $caller->email,
            'receiver_name' => $receiver->name,
            'receiver_number' => $receiver->phone ?? $receiver->email,
            'call_type' => 'outgoing', // From caller's perspective
            'call_status' => 'initiated',
            'started_at' => now(),
        ]);

        try {
            // Send VoIP notification to receiver
            $receiver->notify(new IncomingCallNotification(
                $caller->name,
                $caller->phone ?? $caller->email,
                $callId
            ));

            // Update call status to ringing
            $callLog->update(['call_status' => 'ringing']);

            return back()->with('success', "VoIP call initiated from {$caller->name} to {$receiver->name}!");
        } catch (\Exception $e) {
            $callLog->update(['call_status' => 'failed']);
            return back()->with('error', 'Failed to initiate call: ' . $e->getMessage());
        }
    }

    /**
     * Update call status (answer, decline, end)
     */
    public function updateCallStatus(Request $request)
    {
        $request->validate([
            'call_id' => 'required|exists:call_logs,call_id',
            'status' => 'required|in:answered,declined,ended,missed',
        ]);

        $callLog = CallLog::where('call_id', $request->call_id)->first();

        $updateData = ['call_status' => $request->status];

        switch ($request->status) {
            case 'answered':
                $updateData['answered_at'] = now();
                break;
            case 'ended':
                $updateData['ended_at'] = now();
                if ($callLog->answered_at) {
                    $updateData['duration_seconds'] = now()->diffInSeconds($callLog->answered_at);
                }
                break;
            case 'declined':
            case 'missed':
                $updateData['ended_at'] = now();
                break;
        }

        $callLog->update($updateData);

        return response()->json([
            'success' => true,
            'message' => "Call status updated to {$request->status}",
            'call_log' => $callLog->fresh()
        ]);
    }

    /**
     * Get call logs for a specific user
     */
    public function getUserCallLogs(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $callLogs = CallLog::with(['caller', 'receiver'])
            ->where(function ($query) use ($request) {
                $query->where('caller_user_id', $request->user_id)
                      ->orWhere('receiver_user_id', $request->user_id);
            })
            ->orderBy('started_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'call_logs' => $callLogs
        ]);
    }

    /**
     * Get call statistics
     */
    public function getCallStats()
    {
        $stats = [
            'total_calls' => CallLog::count(),
            'answered_calls' => CallLog::where('call_status', 'answered')->count(),
            'missed_calls' => CallLog::where('call_status', 'missed')->count(),
            'declined_calls' => CallLog::where('call_status', 'declined')->count(),
            'average_duration' => CallLog::whereNotNull('duration_seconds')->avg('duration_seconds'),
        ];

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }
}
