# Laravel APN (Apple Push Notification) Demo

This Laravel application demonstrates how to send Apple Push Notifications using the `laravel-notification-channels/apn` package.

## 🚀 Quick Start

### 1. Access the Demo
The application is now running at: **http://127.0.0.1:8000**

The main demo page is available at: **http://127.0.0.1:8000/apn**

### 2. Database Configuration
✅ **Already Configured:**
- Database: `molema_live`
- Host: `localhost`
- Username: `root`
- Password: (empty)

✅ **Test Users Created:**
- <PERSON> (<EMAIL>)
- <PERSON> (<EMAIL>)
- <PERSON> (<EMAIL>)

## 📱 Testing the APN Functionality

### Step 1: Configure APN Credentials (Required for Real Notifications)

To send actual push notifications, you need to update your `.env` file with valid Apple Developer credentials:

```env
APN_KEY_ID=W4LYFB48MX
APN_TEAM_ID=T647P9HAZS
APN_BUNDLE_ID=com.yourapp.bundleid
APN_PRIVATE_KEY_PATH=  # Leave empty to use default: storage/app/pem/AuthKey_W4LYFB48MX.p8
APN_PRODUCTION=false
```

**How to get these credentials:**
1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Create an APNs Key under "Keys"
4. Download the .p8 file and place it in `storage/app/pem/AuthKey_W4LYFB48MX.p8`
5. Note your Key ID and Team ID

### Step 2: Test the Demo Interface

Visit **http://127.0.0.1:8000/apn** and you'll see:

#### Configuration Status Panel
- Shows which APN credentials are configured
- Displays database connection status

#### Users Panel
- Lists all users in the database
- Shows which users have APN tokens set

#### Update APN Token Form
- Select a user and enter a device token
- **For testing without real devices, use a fake token like:** `1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef`

#### Send Notification Forms
- **Send to User:** Select a user with an APN token and send a notification
- **Send Anonymous:** Send directly to an APN token without a user model

### Step 3: Testing Scenarios

#### Scenario A: Test with Fake Token (No Real Device)
1. Go to http://127.0.0.1:8000/apn
2. In "Update User APN Token" section:
   - Select "John Doe"
   - Enter token: `1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef`
   - Click "Update Token"
3. In "Send Notification to User" section:
   - Select "John Doe"
   - Enter title: "Test Notification"
   - Enter message: "Hello from Laravel!"
   - Click "Send Notification"
4. **Expected Result:** Without valid APN credentials, you'll see an error message showing the configuration issue

#### Scenario B: Test with Real APN Credentials
1. Configure valid APN credentials in `.env`
2. Use a real device token from your iOS app
3. Follow the same steps as Scenario A
4. **Expected Result:** Notification should be delivered to the device

#### Scenario C: Test Anonymous Notifications
1. In "Send Anonymous Notification" section:
   - Enter any APN token
   - Enter title and message
   - Click "Send Anonymous"
2. This tests sending notifications without a user model

## 🛠 Available Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Redirects to APN demo |
| GET | `/apn` | Main APN demo interface |
| POST | `/apn/send-test` | Send notification to a user |
| POST | `/apn/update-token` | Update user's APN token |
| POST | `/apn/send-anonymous` | Send anonymous notification |

## 🔧 Technical Details

### Package Used
- `laravel-notification-channels/apn` v5.5.0
- Uses JWT token authentication with Apple's APNs

### Key Files Created/Modified
- `app/Notifications/ApnTestNotification.php` - Notification class
- `app/Http/Controllers/ApnController.php` - Demo controller
- `app/Models/User.php` - Added APN token support
- `resources/views/apn/index.blade.php` - Demo interface
- `config/broadcasting.php` - APN configuration
- `routes/web.php` - Demo routes

### Database Changes
- Added `apn_token` column to `users` table
- Created test users via seeder

## 🧪 Testing Without Real Devices

Even without real iOS devices or valid APN credentials, you can:

1. **Test the Interface:** All forms and UI components work
2. **Test Validation:** Try submitting forms with invalid data
3. **Test Error Handling:** See how the app handles missing credentials
4. **Test Database Operations:** Update tokens and see them reflected in the UI

## 🚨 Troubleshooting

### Common Issues:

1. **"User does not have an APN token set"**
   - Solution: Use the "Update User APN Token" form to set a token first

2. **APN Configuration Errors**
   - Solution: Ensure all APN environment variables are set correctly
   - Check that private key format is correct (includes BEGIN/END lines)

3. **Database Connection Issues**
   - Solution: Verify MySQL is running and database `molema_live` exists

### Logs
Check Laravel logs for detailed error messages:
```bash
tail -f storage/logs/laravel.log
```

## 📚 Next Steps

1. **Get Real APN Credentials:** Follow Apple's documentation to set up proper APNs
2. **Create iOS App:** Build an iOS app that registers for push notifications
3. **Integrate with Your App:** Use the notification patterns shown in this demo
4. **Add VoIP Support:** The package also supports VoIP notifications (see ApnVoipChannel)

## 🎯 Demo Features Included

✅ JWT Token Authentication setup
✅ User-based notifications
✅ Anonymous notifications
✅ Token management
✅ Error handling
✅ Configuration validation
✅ Bootstrap UI
✅ Test data seeding
✅ MySQL database integration

The demo is fully functional and ready for testing!
