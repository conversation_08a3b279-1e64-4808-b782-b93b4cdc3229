<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\Apn\ApnVoipChannel;
use NotificationChannels\Apn\ApnVoipMessage;

class IncomingCallNotification extends Notification
{
    use Queueable;

    private $callerName;
    private $callerNumber;
    private $callId;

    /**
     * Create a new notification instance.
     */
    public function __construct($callerName, $callerNumber, $callId = null)
    {
        $this->callerName = $callerName;
        $this->callerNumber = $callerNumber;
        $this->callId = $callId ?: uniqid('call_');
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [ApnVoipChannel::class];
    }

    /**
     * Get the VoIP representation of the notification.
     */
    public function toApnVoip(object $notifiable): ApnVoipMessage
    {
        return ApnVoipMessage::create()
            ->custom([
                'call_id' => $this->callId,
                'caller_name' => $this->callerName,
                'caller_number' => $this->callerNumber,
                'call_type' => 'incoming',
                'timestamp' => now()->toISOString(),
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'call_id' => $this->callId,
            'caller_name' => $this->callerName,
            'caller_number' => $this->callerNumber,
            'call_type' => 'incoming',
            'timestamp' => now()->toISOString(),
        ];
    }
}
