<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use NotificationChannels\Apn\ApnChannel;
use NotificationChannels\Apn\ApnMessage;
use App\Notifications\ApnTestNotification;
use Illuminate\Support\Facades\Notification;

class TestApnConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'apn:test-config';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test APN configuration and diagnose issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing APN Configuration...');
        $this->newLine();

        // Check environment variables
        $this->info('📋 Environment Variables:');
        $this->line('APN_KEY_ID: ' . (env('APN_KEY_ID') ?: 'NOT SET'));
        $this->line('APN_TEAM_ID: ' . (env('APN_TEAM_ID') ?: 'NOT SET'));
        $this->line('APN_BUNDLE_ID: ' . (env('APN_BUNDLE_ID') ?: 'NOT SET'));
        $this->line('APN_PRODUCTION: ' . (env('APN_PRODUCTION', false) ? 'true' : 'false'));
        $this->newLine();

        // Check private key file
        $keyPath = env('APN_PRIVATE_KEY_PATH') ?: storage_path('app/pem/AuthKey_W4LYFB48MX.p8');
        $this->info('🔑 Private Key File:');
        $this->line('Path: ' . $keyPath);

        if (file_exists($keyPath)) {
            $this->line('✅ File exists');
            $this->line('Size: ' . filesize($keyPath) . ' bytes');
            $this->line('Readable: ' . (is_readable($keyPath) ? 'Yes' : 'No'));

            // Check file content
            $content = file_get_contents($keyPath);
            if (strpos($content, '-----BEGIN PRIVATE KEY-----') !== false) {
                $this->line('✅ Valid .p8 format detected');
            } else {
                $this->error('❌ Invalid .p8 format - missing BEGIN PRIVATE KEY header');
            }
        } else {
            $this->error('❌ File does not exist');
        }
        $this->newLine();

        // Check broadcasting config
        $this->info('⚙️ Broadcasting Configuration:');
        $config = config('broadcasting.connections.apn');
        if ($config) {
            $this->line('✅ APN config found in broadcasting.php');
            foreach ($config as $key => $value) {
                if ($key === 'private_key_path') {
                    $this->line("$key: " . ($value ?: 'DEFAULT'));
                } else {
                    $this->line("$key: " . ($value ?: 'NOT SET'));
                }
            }
        } else {
            $this->error('❌ APN config not found in broadcasting.php');
        }
        $this->newLine();

        // Test notification creation
        $this->info('🧪 Testing Notification Creation:');
        try {
            $notification = new ApnTestNotification('Test', 'Test message');
            $this->line('✅ ApnTestNotification created successfully');

            // Test message creation
            $message = $notification->toApn((object)['id' => 1]);
            $this->line('✅ ApnMessage created successfully');
        } catch (\Exception $e) {
            $this->error('❌ Error creating notification: ' . $e->getMessage());
        }
        $this->newLine();

        // Test with fake token
        $this->info('📱 Testing with fake token:');
        try {
            $fakeToken = '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

            Notification::route('apn', $fakeToken)
                ->notify(new ApnTestNotification('Config Test', 'Testing APN configuration'));

            $this->line('✅ Notification sent (or attempted) - check logs for details');
        } catch (\Exception $e) {
            $this->error('❌ Error sending notification: ' . $e->getMessage());
            $this->line('Error details: ' . $e->getFile() . ':' . $e->getLine());
        }

        $this->newLine();
        $this->info('🏁 Test completed. Check the results above for any issues.');
    }
}
