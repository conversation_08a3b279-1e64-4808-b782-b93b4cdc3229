<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('call_logs', function (Blueprint $table) {
            $table->id();
            $table->string('call_id')->unique();
            $table->foreignId('caller_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('receiver_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('caller_name');
            $table->string('caller_number');
            $table->string('receiver_name')->nullable();
            $table->string('receiver_number')->nullable();
            $table->enum('call_type', ['incoming', 'outgoing', 'missed']);
            $table->enum('call_status', ['initiated', 'ringing', 'answered', 'ended', 'missed', 'declined']);
            $table->timestamp('started_at');
            $table->timestamp('answered_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->integer('duration_seconds')->nullable(); // Call duration in seconds
            $table->json('metadata')->nullable(); // Additional call data
            $table->timestamps();

            $table->index(['caller_user_id', 'started_at']);
            $table->index(['receiver_user_id', 'started_at']);
            $table->index(['call_status', 'started_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('call_logs');
    }
};
