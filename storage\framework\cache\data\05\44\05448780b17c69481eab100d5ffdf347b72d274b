1748592113O:13:"<PERSON><PERSON><PERSON>\Client":8:{s:28:" <PERSON><PERSON><PERSON>\Client notifications";a:0:{}s:27:" <PERSON><PERSON><PERSON>\Client authProvider";O:25:"<PERSON><PERSON><PERSON>\AuthProvider\Token":7:{s:32:" <PERSON><PERSON><PERSON>\AuthProvider\Token token";s:274:"eyJhbGciOiJFUzUxMiIsImtpZCI6Ilc0TFlGQjQ4TVgifQ.**************************************************.AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKwfjBLqLge-8I_dsSeIKZhrpKmMnH2KlpCpbbzfQ0wiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAK-xSHjNu89oPNd-Rl9ElUqLzQbNAbPeO5UXIwf-qlfM";s:41:" <PERSON><PERSON><PERSON>\AuthProvider\Token privateKeyPath";s:67:"C:\wamp\www\laravel-voip-demo\storage\app\pem\AuthKey_W4LYFB48MX.p8";s:44:" Pushok\AuthProvider\Token privateKeyContent";N;s:43:" Pushok\AuthProvider\Token privateKeySecret";N;s:32:" Pushok\AuthProvider\Token keyId";s:10:"W4LYFB48MX";s:33:" Pushok\AuthProvider\Token teamId";s:10:"T647P9HAZS";s:38:" Pushok\AuthProvider\Token appBundleId";s:15:"com.example.app";}s:30:" Pushok\Client isProductionEnv";b:0;s:35:" Pushok\Client nbConcurrentRequests";i:20;s:39:" Pushok\Client maxConcurrentConnections";i:1;s:35:" Pushok\Client autoCloseConnections";b:1;s:30:" Pushok\Client curlMultiHandle";N;s:26:" Pushok\Client curlOptions";a:0:{}}