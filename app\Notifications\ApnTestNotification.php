<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\Apn\ApnChannel;
use NotificationChannels\Apn\ApnMessage;

class ApnTestNotification extends Notification
{
    use Queueable;

    private $title;
    private $body;

    /**
     * Create a new notification instance.
     */
    public function __construct($title = 'Test Notification', $body = 'This is a test APN notification from Laravel!')
    {
        $this->title = $title;
        $this->body = $body;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [ApnChannel::class];
    }

    /**
     * Get the APN representation of the notification.
     */
    public function toApn(object $notifiable): ApnMessage
    {
        return ApnMessage::create()
            ->badge(1)
            ->title($this->title)
            ->body($this->body)
            ->sound('default');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
        ];
    }
}
