<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel APN Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4">Laravel APN (Apple Push Notification) Demo</h1>
                
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Configuration Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Configuration Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>APN Key ID:</strong> {{ env('APN_KEY_ID') ? '✓ Set' : '✗ Not Set' }}</p>
                                <p><strong>APN Team ID:</strong> {{ env('APN_TEAM_ID') ? '✓ Set' : '✗ Not Set' }}</p>
                                <p><strong>APN Bundle ID:</strong> {{ env('APN_BUNDLE_ID') ? env('APN_BUNDLE_ID') : '✗ Not Set' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>APN Private Key:</strong> {{ env('APN_PRIVATE_KEY') ? '✓ Set' : '✗ Not Set' }}</p>
                                <p><strong>Production Mode:</strong> {{ env('APN_PRODUCTION', false) ? 'Yes' : 'No' }}</p>
                                <p><strong>Database:</strong> {{ env('DB_DATABASE') }}</p>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <strong>Note:</strong> To send actual push notifications, you need to configure your APN credentials in the .env file.
                            See the README for instructions on obtaining these from Apple Developer Console.
                        </div>
                    </div>
                </div>

                <!-- Users List -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Users</h5>
                    </div>
                    <div class="card-body">
                        @if($users->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>APN Token</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($users as $user)
                                            <tr>
                                                <td>{{ $user->id }}</td>
                                                <td>{{ $user->name }}</td>
                                                <td>{{ $user->email }}</td>
                                                <td>
                                                    @if($user->apn_token)
                                                        <span class="badge bg-success">Set</span>
                                                        <small class="text-muted d-block">{{ substr($user->apn_token, 0, 20) }}...</small>
                                                    @else
                                                        <span class="badge bg-warning">Not Set</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" onclick="setUserId({{ $user->id }})">
                                                        Send Notification
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                No users found. Create some users first using Laravel Tinker or Seeder.
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Update APN Token Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Update User APN Token</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('apn.update-token') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="user_id_token" class="form-label">User ID</label>
                                        <select class="form-select" id="user_id_token" name="user_id" required>
                                            <option value="">Select User</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->id }} - {{ $user->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="mb-3">
                                        <label for="apn_token" class="form-label">APN Token</label>
                                        <input type="text" class="form-control" id="apn_token" name="apn_token" 
                                               placeholder="Enter APN device token" required>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-success d-block">Update Token</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Send Notification to User Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Send Notification to User</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('apn.send-test') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="user_id_notification" class="form-label">User ID</label>
                                        <select class="form-select" id="user_id_notification" name="user_id" required>
                                            <option value="">Select User</option>
                                            @foreach($users->where('apn_token', '!=', null) as $user)
                                                <option value="{{ $user->id }}">{{ $user->id }} - {{ $user->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="Test Notification" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="body" class="form-label">Message</label>
                                        <input type="text" class="form-control" id="body" name="body" 
                                               value="This is a test APN notification from Laravel!" required>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block">Send Notification</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Send Anonymous Notification Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Send Anonymous Notification</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('apn.send-anonymous') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="apn_token_anonymous" class="form-label">APN Token</label>
                                        <input type="text" class="form-control" id="apn_token_anonymous" name="apn_token" 
                                               placeholder="Enter APN device token" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="title_anonymous" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title_anonymous" name="title" 
                                               value="Anonymous Notification" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="body_anonymous" class="form-label">Message</label>
                                        <input type="text" class="form-control" id="body_anonymous" name="body" 
                                               value="This is an anonymous APN notification!" required>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-warning d-block">Send Anonymous</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5>Instructions</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Create Users:</strong> Use Laravel Tinker to create test users: <code>php artisan tinker</code> then <code>User::factory()->create(['name' => 'Test User', 'email' => '<EMAIL>']);</code></li>
                            <li><strong>Configure APN:</strong> Update your .env file with valid APN credentials from Apple Developer Console</li>
                            <li><strong>Set APN Tokens:</strong> Use the form above to set APN device tokens for users</li>
                            <li><strong>Send Notifications:</strong> Use the forms above to send test notifications</li>
                        </ol>
                        
                        <h6 class="mt-4">APN Configuration Required:</h6>
                        <ul>
                            <li><code>APN_KEY_ID</code> - Your APN Key ID from Apple Developer Console</li>
                            <li><code>APN_TEAM_ID</code> - Your Team ID from Apple Developer Console</li>
                            <li><code>APN_BUNDLE_ID</code> - Your app's bundle identifier</li>
                            <li><code>APN_PRIVATE_KEY</code> - Your APN private key content (.p8 file content)</li>
                            <li><code>APN_PRODUCTION</code> - Set to true for production, false for sandbox</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setUserId(userId) {
            document.getElementById('user_id_notification').value = userId;
        }
    </script>
</body>
</html>
