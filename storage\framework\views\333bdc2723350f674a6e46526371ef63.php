<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoIP Call Logs - Laravel Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-phone"></i> VoIP Call Management</h1>
                    <a href="<?php echo e(route('apn.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-bell"></i> Back to APN Demo
                    </a>
                </div>
                
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Call Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                                <h5 class="card-title" id="total-calls"><?php echo e($callLogs->total()); ?></h5>
                                <p class="card-text">Total Calls</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-phone-volume fa-2x text-success mb-2"></i>
                                <h5 class="card-title" id="answered-calls">-</h5>
                                <p class="card-text">Answered</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-phone-slash fa-2x text-warning mb-2"></i>
                                <h5 class="card-title" id="missed-calls">-</h5>
                                <p class="card-text">Missed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h5 class="card-title" id="avg-duration">-</h5>
                                <p class="card-text">Avg Duration</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Initiate Call Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-phone-alt"></i> Initiate VoIP Call</h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('voip.initiate-call')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label for="caller_user_id" class="form-label">Caller</label>
                                        <select class="form-select" id="caller_user_id" name="caller_user_id" required>
                                            <option value="">Select Caller</option>
                                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?> (<?php echo e($user->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label for="receiver_user_id" class="form-label">Receiver</label>
                                        <select class="form-select" id="receiver_user_id" name="receiver_user_id" required>
                                            <option value="">Select Receiver</option>
                                            <?php $__currentLoopData = $users->where('apn_token', '!=', null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?> (<?php echo e($user->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-success d-block w-100">
                                            <i class="fas fa-phone"></i> Call
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Call Logs -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Call Logs</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshCallLogs()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if($callLogs->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Call ID</th>
                                            <th>Caller</th>
                                            <th>Receiver</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Started</th>
                                            <th>Duration</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="call-logs-table">
                                        <?php $__currentLoopData = $callLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $call): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr data-call-id="<?php echo e($call->call_id); ?>">
                                                <td>
                                                    <small class="text-muted"><?php echo e($call->call_id); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo e($call->caller_name); ?></strong><br>
                                                    <small class="text-muted"><?php echo e($call->caller_number); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo e($call->receiver_name); ?></strong><br>
                                                    <small class="text-muted"><?php echo e($call->receiver_number); ?></small>
                                                </td>
                                                <td>
                                                    <?php if($call->call_type === 'incoming'): ?>
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-arrow-down"></i> Incoming
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-arrow-up"></i> Outgoing
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo e($call->call_status === 'answered' ? 'success' : 
                                                        ($call->call_status === 'missed' ? 'warning' : 
                                                        ($call->call_status === 'declined' ? 'danger' : 'secondary'))); ?>">
                                                        <?php echo e(ucfirst($call->call_status)); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo e($call->started_at->format('M d, Y H:i')); ?><br>
                                                    <small class="text-muted"><?php echo e($call->started_at->diffForHumans()); ?></small>
                                                </td>
                                                <td>
                                                    <?php echo e($call->duration_formatted); ?>

                                                </td>
                                                <td>
                                                    <?php if(in_array($call->call_status, ['initiated', 'ringing'])): ?>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-success" onclick="updateCallStatus('<?php echo e($call->call_id); ?>', 'answered')">
                                                                <i class="fas fa-phone"></i>
                                                            </button>
                                                            <button class="btn btn-danger" onclick="updateCallStatus('<?php echo e($call->call_id); ?>', 'declined')">
                                                                <i class="fas fa-phone-slash"></i>
                                                            </button>
                                                        </div>
                                                    <?php elseif($call->call_status === 'answered'): ?>
                                                        <button class="btn btn-sm btn-warning" onclick="updateCallStatus('<?php echo e($call->call_id); ?>', 'ended')">
                                                            <i class="fas fa-phone-slash"></i> End
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                <?php echo e($callLogs->links()); ?>

                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No call logs found</h5>
                                <p class="text-muted">Initiate your first VoIP call using the form above.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> VoIP Call Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>How VoIP Calls Work:</h6>
                                <ol>
                                    <li><strong>Initiate Call:</strong> Select caller and receiver, then click "Call"</li>
                                    <li><strong>VoIP Push:</strong> Receiver gets a VoIP push notification</li>
                                    <li><strong>Call Actions:</strong> Answer, decline, or end calls using the buttons</li>
                                    <li><strong>Call Logs:</strong> All calls are tracked with timestamps and duration</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>Requirements:</h6>
                                <ul>
                                    <li>Users must have APN tokens set</li>
                                    <li>Valid APN credentials configured</li>
                                    <li>iOS app with VoIP capabilities</li>
                                    <li>CallKit integration for native call UI</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load call statistics
        loadCallStats();

        function loadCallStats() {
            fetch('<?php echo e(route("voip.call-stats")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('answered-calls').textContent = data.stats.answered_calls;
                        document.getElementById('missed-calls').textContent = data.stats.missed_calls;
                        
                        const avgDuration = data.stats.average_duration;
                        if (avgDuration) {
                            const minutes = Math.floor(avgDuration / 60);
                            const seconds = Math.floor(avgDuration % 60);
                            document.getElementById('avg-duration').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                        } else {
                            document.getElementById('avg-duration').textContent = '0:00';
                        }
                    }
                });
        }

        function updateCallStatus(callId, status) {
            fetch('<?php echo e(route("voip.update-call-status")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    call_id: callId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload(); // Refresh page to show updated status
                } else {
                    alert('Failed to update call status');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to update call status');
            });
        }

        function refreshCallLogs() {
            location.reload();
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            loadCallStats();
        }, 30000);
    </script>
</body>
</html>
<?php /**PATH C:\wamp\www\laravel-voip-demo\resources\views/voip/index.blade.php ENDPATH**/ ?>